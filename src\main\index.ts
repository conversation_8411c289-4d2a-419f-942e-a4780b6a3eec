import { app, shell, BrowserWindow, ipcMain, session, globalShortcut } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/app.ico?asset'
// import udpService from './services/udpService'
import httpService from './services/httpService' // 导入 HTTP 服务
import fs from 'fs'
import path from 'path'
import url from 'url'
import { Key, keyboard } from '@nut-tree-fork/nut-js'
import { autoUpdater } from 'electron-updater'
// 删除重复的导入
// import { app, ipcMain } from 'electron'
import machineId from 'node-machine-id'
import {kill} from './openSnap.ts'

// 根据构建模式设置不同的应用标识，确保test和prod版本可以同时运行
const buildMode = import.meta.env.MODE || 'prod';

// 为不同版本设置不同的应用名称，这样它们就会被视为不同的应用
// 必须在导入readConfig之前设置，这样userData路径才会正确
if (buildMode === 'test') {
  app.setName('popofifi-test');
} else {
  app.setName('popofifi-prod');
}

// 现在导入config，此时app.setName()已经生效
import getConfig from './readConfig'
import {setLocalConfig} from './readConfig'
import { getVersions, getCurrentVersion } from './getVersion'
const machineID = machineId.machineIdSync(true)

// 配置 electron-updater
function setupAutoUpdater() {
  // 设置更新服务器地址
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: 'http://localhost:8080'
  });

  // 配置更新选项
  autoUpdater.autoDownload = false; // 不自动下载，让用户选择
  autoUpdater.autoInstallOnAppQuit = true; // 应用退出时自动安装
  autoUpdater.forceDevUpdateConfig = true; // 在开发模式下也允许更新检查

  // 监听更新事件
  autoUpdater.on('checking-for-update', () => {
    console.log('正在检查更新...');
  });

  autoUpdater.on('update-available', (info) => {
    console.log('发现新版本:', info.version);
  });

  autoUpdater.on('update-not-available', (info) => {
    console.log('已是最新版本:', info.version);
  });

  autoUpdater.on('error', (err) => {
    console.error('更新检查失败:', err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    console.log(`下载进度: ${progressObj.percent}%`);
  });

  autoUpdater.on('update-downloaded', (info) => {
    console.log('更新下载完成:', info.version);
  });
}

// 现在请求单实例锁，由于应用名称不同，test和prod版本不会互相冲突
const gotTheLock = app.requestSingleInstanceLock();

app.commandLine.appendSwitch('ignore-certificate-errors')
let isTest = import.meta.env.MODE === 'test'
let isDev = process.env.NODE_ENV === 'development'
if (!gotTheLock) {
  console.log('应用已在运行，禁止多开！');
  app.quit(); // 退出当前进程
} 

function createWindow() {
  // 获取配置
  const config = getConfig();

  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 500,
    height: 948,
    x: isDev ? 200 : 0,
    y: 0,
    frame: false, // 隐藏默认标题栏
    show: false,
    autoHideMenuBar: true,
    alwaysOnTop: (config.APP.alwaysOnTop !== undefined)? config.APP.alwaysOnTop : !isDev,
    fullscreen: (config.APP.fullScreen !== undefined)? config.APP.fullScreen : (!isDev || isTest),
    ...(process.platform === 'linux' ? { icon } : {}),
    icon,
    webPreferences: {
      allowRunningInsecureContent: true,
      preload: join(__dirname, '../preload/index.js'),
      devTools: true,
      sandbox: false,
      nodeIntegration: true,
    }
  })
  if (isDev || isTest) mainWindow.webContents.openDevTools()
  
  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 窗口创建后扫描音频文件并发送到渲染进程
  // mainWindow.webContents.on('did-finish-load', () => {})
  // mainWindow.webContents.setWindowOpenHandler((details) => {
  //   shell.openExternal(details.url)
  //   return { action: 'deny' }
  // })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
  return mainWindow
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // 初始化自动更新
  setupAutoUpdater();

  const { session } = require('electron')
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    if (permission === 'media') {
      callback(true) // 允许媒体设备访问
    } else {
      callback(false)
    }
  })

  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // udpService.initUdpServices()
  httpService.initHttpService() // 初始化 HTTP 服务

  // ipcMain.handle('send_udp_message', async (_, { message }) => {
  //   try {
  //     const result = await udpService.sendUdpMessage(message)
  //     return result
  //   } catch (error) {
  //     console.error('UDP message handler error:', error)
  //     throw error
  //   }
  // })

  ipcMain.handle('kuaijiejian', async (_, message) => {
    try {
      console.log('message', message)
      if (message.name == 'huanlianopen'){
        await keyboard.type(Key.LeftControl, Key.Z);
      }else if (message.name == 'huanlianclose'){
        await keyboard.type(Key.LeftControl, Key.Z);
      }
    } catch (error) {
      console.error('UDP message handler error:', error)
      throw error
    }
  })

  ipcMain.handle('get-machine-info', async () => {
    console.log(machineID)
    return {
      machineId: machineID
    }
  })
  ipcMain.handle('get-app-path', () => {
     console.log(path.dirname(app.getPath('exe')), 'dsdsdsd')
    return path.dirname(app.getPath('exe'))
  })

  // 在app.whenReady().then()中添加：
  ipcMain.handle('get-app-dev', () => {
    return process.env.NODE_ENV;
  });

  ipcMain.handle('get-config', () => {
    return getConfig()
  });

  ipcMain.handle('set-local-config', (_, key, value) => {
    console.log(key)
    console.log(value)
    setLocalConfig(key,value)
  });

  // 获取版本信息
  ipcMain.handle('get-version', () => {
    return getCurrentVersion();
  });

  // 获取完整版本信息数组
  ipcMain.handle('get-version-info', () => {
    return getVersions();
  });

  // 重启应用
  ipcMain.handle('restart-app', () => {
    app.relaunch();
    app.exit(0);
  });

  // 检查更新
  ipcMain.handle('check-for-updates', async () => {
    try {
      const result = await autoUpdater.checkForUpdates();
      return { success: true, updateInfo: result?.updateInfo };
    } catch (error) {
      console.error('检查更新失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  });

  // 下载更新
  ipcMain.handle('download-update', async () => {
    try {
      await autoUpdater.downloadUpdate();
      return { success: true };
    } catch (error) {
      console.error('下载更新失败:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { success: false, error: errorMessage };
    }
  });

  // 安装更新并重启
  ipcMain.handle('install-update', () => {
    autoUpdater.quitAndInstall();
  });

  let mainWindow= createWindow()

  // 全局键盘监听
  globalShortcut.register('F12', () => {
    if (mainWindow.webContents.isDevToolsOpened()) {
      mainWindow.webContents.closeDevTools()
    } else {
      mainWindow.webContents.openDevTools()
    }
  })



  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  // app.on('browser-window-created', (_, window) => {
  //   optimizer.watchWindowShortcuts(window)
  // })
  // 在主进程代码中添加

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
// 添加关闭应用程序的处理程序
ipcMain.on('close-app', () => {
  app.quit()
})
// 在应用退出前关闭 HTTP 服务
app.on('will-quit', () => {
  kill()
  // 注销快捷键
  globalShortcut.unregisterAll();
  httpService.closeHttpService()
})
